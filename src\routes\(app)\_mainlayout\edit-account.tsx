import {
	create<PERSON>ile<PERSON><PERSON><PERSON>,
	useNavigate,
	useRouter,
} from "@tanstack/react-router";
import { useAuthStore } from "@/features/auth/store";
import { useShallow } from "zustand/react/shallow";
import { But<PERSON> } from "@/components/ui/button";
import { Camera, ChevronLeft } from "react-feather";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
	UpdateUserValidation,
	updateUserValidation,
} from "@/features/auth/schema";
import {
	useGetUser,
	useUpdateUser,
	useGetProfilePictureUploadUrl,
	useGetProfilePictureUrl,
	uploadProfilePicture,
} from "@/lib/queries/user.query";
import { useEffect, useRef } from "react";
import { Form } from "@/components/ui/form";
import UpdateUserForm from "@/components/forms/UpdateUserForm";
import { toast } from "@/hooks/use-toast";
import { useQueryClient } from "@tanstack/react-query";

// Custom simple loading state component
const LoadingBlock = ({ className = "" }: { className?: string }) => (
	<div className={`animate-pulse bg-slate-100 rounded-md ${className}`}></div>
);

const AccountEditPage = () => {
	const navigate = useNavigate();
	const { history } = useRouter();
	const queryClient = useQueryClient();
	const fileInputRef = useRef<HTMLInputElement>(null);

	const { user } = useAuthStore(
		useShallow((state) => ({
			user: state.user,
		}))
	);

	// Fetch user details from API using firebase ID
	const { data: userData, isLoading } = useGetUser(user?.uid || "");
	const apiUser = userData?.data?.data;

	// Get profile picture URL
	const { data: profilePictureData } = useGetProfilePictureUrl(user?.uid || "");
	const profilePictureUrl = profilePictureData?.data?.data?.url;

	// Mutations
	const { mutateAsync: updateUser, isPending } = useUpdateUser();
	const { mutateAsync: getUploadUrl, isPending: isUploadUrlPending } =
		useGetProfilePictureUploadUrl();

	// Extract user details - prioritize database name if it exists and is not empty
	const displayName =
		apiUser?.name && apiUser.name.trim() !== ""
			? apiUser.name
			: user?.displayName || user?.email?.split("@")[0] || "User";

	// Get user initials for avatar fallback
	const getInitials = (name: string) => {
		if (!name) return "??";
		return name
			.split(" ")
			.map((part) => part.charAt(0).toUpperCase())
			.join("")
			.substring(0, 2);
	};

	const form = useForm<UpdateUserValidation>({
		resolver: zodResolver(updateUserValidation),
		defaultValues: {
			name: "",
			email: "",
			city: "",
			phoneNumber: "",
			institute: "",
			educationBackground: "",
			currentClass: "",
			subjectGroup: [],
			targetEntryTests: [],
		},
	});

	// Update form values when API data is loaded
	useEffect(() => {
		if (apiUser) {
			form.reset({
				// Prioritize name from database if available, otherwise use auth provider name
				name:
					apiUser.name && apiUser.name.trim() !== ""
						? apiUser.name
						: user?.displayName || "",
				email: user?.email || "",
				city: apiUser.city || "",
				phoneNumber: apiUser.phoneNumber || "",
				institute: apiUser.institute || "",
				educationBackground: apiUser.educationBackground || "",
				currentClass: apiUser.currentClass || "",
				subjectGroup: apiUser.subjectGroup || [],
				targetEntryTests: apiUser.targetEntryTests || [],
			});
		}
	}, [apiUser, user, form]);

	const onSubmit = async (values: UpdateUserValidation) => {
		try {
			// Only send fields with values
			const updateData: Partial<UpdateUserValidation> = {};

			Object.entries(values).forEach(([key, value]) => {
				if (value !== undefined && value !== null && value !== "") {
					// Type-safe way to assign values
					const typedKey = key as keyof UpdateUserValidation;
					(updateData as any)[typedKey] = value;
				}
			});

			// Make API call
			await updateUser(updateData);

			// Show success message
			toast({
				title: "Profile Updated",
				description: "Your profile has been successfully updated.",
			});

			// Invalidate the user query to refetch updated data
			queryClient.invalidateQueries({ queryKey: ["user", user?.uid] });

			// Navigate back to account page
			navigate({ to: "/account" as any });
		} catch (error) {
			console.error("Error updating user:", error);
			toast({
				title: "Update Failed",
				description:
					"There was a problem updating your profile. Please try again.",
				variant: "destructive",
			});
		}
	};

	const handleProfilePictureClick = () => {
		fileInputRef.current?.click();
	};

	const handleProfilePictureChange = async (
		e: React.ChangeEvent<HTMLInputElement>
	) => {
		const file = e.target.files?.[0];
		if (!file) return;

		try {
			// Validate file size and type
			if (file.size > 1024 * 1024) {
				toast({
					title: "File too large",
					description: "Image must be less than 1MB",
					variant: "destructive",
				});
				return;
			}

			const validTypes = ["image/jpeg", "image/jpg", "image/png"];
			if (!validTypes.includes(file.type)) {
				toast({
					title: "Invalid file type",
					description: "Only JPG, JPEG and PNG formats are allowed",
					variant: "destructive",
				});
				return;
			}

			// Get upload URL
			const uploadUrlResponse = await getUploadUrl();
			if (!uploadUrlResponse?.data?.success) {
				throw new Error("Could not get upload URL");
			}

			const uploadUrl = uploadUrlResponse.data.data.url;

			// Inform user that upload is in progress
			toast({
				title: "Uploading",
				description: "Uploading your profile picture...",
			});

			// Upload the file
			await uploadProfilePicture(file, uploadUrl);

			// Invalidate the profile picture URL query to refetch the new URL
			queryClient.invalidateQueries({
				queryKey: ["profilePictureUrl", user?.uid],
			});

			toast({
				title: "Success",
				description: "Profile picture updated successfully",
			});
		} catch (error) {
			console.error("Error uploading profile picture:", error);
			toast({
				title: "Upload Failed",
				description:
					error instanceof Error
						? error.message
						: "Failed to upload profile picture",
				variant: "destructive",
			});
		} finally {
			// Reset the file input
			if (fileInputRef.current) {
				fileInputRef.current.value = "";
			}
		}
	};

	const handleGoBack = () => {
		history.go(-1);
	};

	return (
		<div className="container mx-auto pb-20 lg:pb-8 px-0">
			{/* Hidden file input */}
			<input
				type="file"
				ref={fileInputRef}
				className="hidden"
				accept="image/jpeg,image/jpg,image/png"
				onChange={handleProfilePictureChange}
			/>

			{/* Profile Section */}
			<div className="flex flex-col items-start w-full">
				{/* Cover Background */}
				<div className="w-full h-[178px] bg-[url(/assets/images/Cover.png)] bg-no-repeat bg-cover relative overflow-hidden"></div>
				{/* <div className="w-full h-[178px] bg-[#5936CD] relative overflow-hidden">
					<div className="absolute w-[515px] h-[329.26px] left-[-67px] top-[-190px]">
						<div className="absolute w-[294.18px] h-[294.18px] right-[713px] top-[-190px] bg-gradient-to-b from-[rgba(239,242,255,0.4)] to-[rgba(239,242,255,0)]"></div>
						<div className="absolute w-[294.18px] h-[294.18px] left-[-67px] top-[-190px] bg-gradient-to-b from-[rgba(239,242,255,0.4)] to-[rgba(239,242,255,0)]"></div>
						<div className="absolute w-[293.71px] h-[294.18px] left-[calc(50%-293.71px/2-389.27px)] top-[-154.92px] bg-gradient-to-b from-[rgba(239,242,255,0.4)] to-[rgba(239,242,255,0)]"></div>
					</div>
				</div> */}
				<button
					onClick={handleGoBack}
					className="lg:hidden absolute top-8 left-4 text-white"
					aria-label="Go back"
				>
					<ChevronLeft size={24} />
				</button>

				{/* User Info Section with Avatar centered */}
				<div className="flex flex-col items-center w-full -mt-[75px]">
					{/* Avatar with camera button */}
					<div className="w-[150px] h-[150px] relative mb-4">
						<Avatar className="w-[150px] h-[150px] border-4 border-[#F8FAFC] bg-[#D9D9D9]">
							<AvatarImage src={profilePictureUrl || ""} alt="User avatar" />
							<AvatarFallback className="text-[48px] text-[#64748B] bg-[#D9D9D9]">
								{getInitials(displayName)}
							</AvatarFallback>
						</Avatar>
						{/* Camera Button Overlay */}
						<Button
							onClick={handleProfilePictureClick}
							variant="ghost"
							disabled={isUploadUrlPending}
							className="absolute bottom-4 right-4 w-9 h-9 rounded-full bg-[#5936CD] border border-[#F8FAFC] p-0 flex items-center justify-center hover:bg-[#4e2eb3]"
						>
							<Camera size={16} className="text-white" />
						</Button>
					</div>
				</div>

				{/* Edit Form */}
				<div className="w-full max-w-7xl mx-auto px-4 mt-4">
					{isLoading ? (
						<div className="space-y-8">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								{[...Array(8)].map((_, i) => (
									<div key={i} className="space-y-4">
										<LoadingBlock className="h-4 w-24" />
										<LoadingBlock className="h-[55px] w-full" />
									</div>
								))}
							</div>
							<div className="flex justify-end gap-4 pt-6">
								<LoadingBlock className="h-[55px] w-[180px]" />
								<LoadingBlock className="h-[55px] w-[180px]" />
							</div>
						</div>
					) : (
						<Form {...form}>
							<form
								onSubmit={form.handleSubmit(onSubmit)}
								className="space-y-8"
							>
								{/* Form fields */}
								<UpdateUserForm
									control={form.control}
									setValue={form.setValue}
									userData={apiUser}
									disableEmail={!!user?.email}
								/>

								{/* Save/Cancel buttons */}
								<div className="flex justify-end gap-4 pt-6">
									<Button
										type="button"
										variant="outline"
										onClick={() => navigate({ to: "/account" as any })}
										className="h-[55px] w-[180px] rounded-[10px] border-[#EFF0F6] shadow-inner"
									>
										Cancel
									</Button>
									<Button
										type="submit"
										className="h-[55px] w-[180px] rounded-[10px] bg-[#5936CD] shadow-md text-white"
										disabled={isPending}
									>
										{isPending ? "Saving..." : "Save"}
									</Button>
								</div>
							</form>
						</Form>
					)}
				</div>
			</div>
		</div>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/edit-account")({
	component: AccountEditPage,
});

export default AccountEditPage;
