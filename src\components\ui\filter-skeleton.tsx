import { Skeleton } from "@/components/ui/skeleton";

const FilterSkeleton = () => {
	return (
		<div className="space-y-3 p-3 bg-white rounded-lg border">
			{/* Clear All Button Skeleton */}
			<Skeleton className="h-8 w-full" />
			
			{/* Filter Dropdowns Skeleton */}
			<div className="space-y-3">
				{Array.from({ length: 5 }).map((_, index) => (
					<div key={index} className="flex flex-col space-y-1">
						{/* Filter Label Skeleton */}
						<Skeleton className="h-4 w-20" />
						
						{/* Filter Dropdown Skeleton */}
						<Skeleton className="h-9 w-full" />
					</div>
				))}
			</div>
		</div>
	);
};

export default FilterSkeleton;
